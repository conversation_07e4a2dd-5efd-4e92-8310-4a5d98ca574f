using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using StackExchange.Redis;
using SmaTrendFollower.Models;
using System.Diagnostics;
using System.Text.Json;

namespace SmaTrendFollower.Services;

/// <summary>
/// Interface for Polygon-based symbol universe management
/// </summary>
public interface IPolygonSymbolUniverseService
{
    /// <summary>
    /// Fetch full symbol list from Polygon API with pagination
    /// </summary>
    Task<IEnumerable<PolygonSymbolInfo>> FetchFullSymbolListAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Get cached symbol list or fetch if not available/stale
    /// </summary>
    Task<IEnumerable<PolygonSymbolInfo>> GetSymbolListAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Force refresh of symbol list from Polygon API
    /// </summary>
    Task<IEnumerable<PolygonSymbolInfo>> RefreshSymbolListAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Check if cached symbol list is valid (not stale)
    /// </summary>
    Task<bool> IsCacheValidAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Get cache statistics and metadata
    /// </summary>
    Task<RedisPolygonSymbolList?> GetCacheDetailsAsync(CancellationToken cancellationToken = default);
}

/// <summary>
/// Service for fetching and caching symbol universe from Polygon API
/// Implements weekly caching with Redis/SQLite fallback
/// </summary>
public sealed class PolygonSymbolUniverseService : IPolygonSymbolUniverseService, IDisposable
{
    private readonly IPolygonClientFactory _polygonFactory;
    private readonly IDatabase? _redis;
    private readonly ConnectionMultiplexer? _connectionMultiplexer;
    private readonly ILogger<PolygonSymbolUniverseService> _logger;
    private readonly PolygonUniverseConfig _config;

    public PolygonSymbolUniverseService(
        IPolygonClientFactory polygonFactory,
        IConfiguration configuration,
        ILogger<PolygonSymbolUniverseService> logger)
    {
        _polygonFactory = polygonFactory ?? throw new ArgumentNullException(nameof(polygonFactory));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));

        // Load configuration
        _config = PolygonUniverseConfig.Default;
        configuration.GetSection("PolygonUniverse").Bind(_config);

        // Initialize Redis connection if configured (optional)
        try
        {
            var redisUrl = configuration["REDIS_URL"]
                ?? configuration.GetSection("Redis")["ConnectionString"]
                ?? Environment.GetEnvironmentVariable("REDIS_CONNECTION_STRING")
                ?? Environment.GetEnvironmentVariable("Redis__ConnectionString")
                ?? "*************:6379"; // env-hierarchy form

            if (!string.IsNullOrEmpty(redisUrl))
            {
                var redisDatabase = int.Parse(configuration["REDIS_DATABASE"] ?? "0");
                var redisPassword = configuration["REDIS_PASSWORD"];

                var configOptions = ConfigurationOptions.Parse(redisUrl);
                configOptions.AbortOnConnectFail = false; // Don't fail if Redis is unavailable
                if (!string.IsNullOrEmpty(redisPassword))
                {
                    configOptions.Password = redisPassword;
                }

                _connectionMultiplexer = ConnectionMultiplexer.Connect(configOptions);
                _redis = _connectionMultiplexer.GetDatabase(redisDatabase);
                _logger.LogInformation("Redis connection established for symbol universe caching");
            }
            else
            {
                _logger.LogInformation("Redis not configured - symbol universe caching disabled");
                _connectionMultiplexer = null;
                _redis = null;
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to connect to Redis - symbol universe caching disabled");
            _connectionMultiplexer = null;
            _redis = null;
        }

        _logger.LogInformation("PolygonSymbolUniverseService initialized with config: PageSize={PageSize}, Markets={Markets}",
            _config.PageSize, string.Join(",", _config.IncludedMarkets));
    }

    public async Task<IEnumerable<PolygonSymbolInfo>> FetchFullSymbolListAsync(CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        var symbols = new List<PolygonSymbolInfo>();
        var apiCallCount = 0;

        _logger.LogInformation("Starting full symbol list fetch from Polygon API");

        try
        {
            var httpClient = _polygonFactory.CreateClient();
            var rateLimitHelper = _polygonFactory.GetRateLimitHelper();
            string? nextUrl = null;

            do
            {
                cancellationToken.ThrowIfCancellationRequested();

                var response = await rateLimitHelper.ExecuteAsync(async () =>
                {
                    var url = nextUrl ?? BuildInitialUrl();
                    var urlWithApiKey = _polygonFactory.AddApiKeyToUrl(url);
                    
                    _logger.LogDebug("Fetching symbols from: {Url}", url);
                    return await httpClient.GetAsync(urlWithApiKey, cancellationToken);
                }, "SymbolListFetch");

                apiCallCount++;

                if (!response.IsSuccessStatusCode)
                {
                    _logger.LogError("Polygon API returned {StatusCode} for symbol list fetch", response.StatusCode);
                    break;
                }

                var content = await response.Content.ReadAsStringAsync(cancellationToken);
                var polygonResponse = JsonSerializer.Deserialize<PolygonTickersResponse>(content);

                if (polygonResponse?.Results == null)
                {
                    _logger.LogWarning("No results in Polygon response. Response content: {Content}", content.Length > 500 ? content.Substring(0, 500) + "..." : content);
                    break;
                }

                _logger.LogDebug("Polygon API returned {ResultCount} symbols in this batch", polygonResponse.Results.Count);

                // Convert and filter symbols
                var filteredSymbols = polygonResponse.Results
                    .Where(ShouldIncludeSymbol)
                    .Select(ConvertToSymbolInfo)
                    .ToList();

                symbols.AddRange(filteredSymbols);
                nextUrl = polygonResponse.NextUrl;

                _logger.LogDebug("Fetched {Count} symbols (total: {Total}), next URL: {NextUrl}",
                    filteredSymbols.Count, symbols.Count, nextUrl != null ? "available" : "none");

                // Check if we've reached the maximum symbols limit
                if (_config.MaxSymbols > 0 && symbols.Count >= _config.MaxSymbols)
                {
                    symbols = symbols.Take(_config.MaxSymbols).ToList();
                    _logger.LogInformation("Reached maximum symbols limit: {MaxSymbols}", _config.MaxSymbols);
                    break;
                }

                // Rate limiting delay
                if (nextUrl != null && _config.DelayBetweenCalls > 0)
                {
                    await Task.Delay(_config.DelayBetweenCalls, cancellationToken);
                }

            } while (nextUrl != null);

            stopwatch.Stop();

            _logger.LogInformation("Fetched {SymbolCount} symbols in {ElapsedMs}ms with {ApiCalls} API calls",
                symbols.Count, stopwatch.ElapsedMilliseconds, apiCallCount);

            // Cache the results if Redis is available
            if (_redis != null)
            {
                await CacheSymbolListAsync(symbols, apiCallCount, stopwatch.Elapsed);
            }

            return symbols;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error fetching symbol list from Polygon API");
            throw;
        }
    }

    public async Task<IEnumerable<PolygonSymbolInfo>> GetSymbolListAsync(CancellationToken cancellationToken = default)
    {
        // Try to get from cache first
        if (_redis != null)
        {
            try
            {
                var cachedList = await GetCachedSymbolListAsync(cancellationToken);
                if (cachedList != null)
                {
                    _logger.LogDebug("Retrieved {Count} symbols from cache", cachedList.Count());
                    return cachedList;
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error retrieving cached symbol list, fetching fresh data");
            }
        }

        // Cache miss or error - fetch fresh data
        return await FetchFullSymbolListAsync(cancellationToken);
    }

    public async Task<IEnumerable<PolygonSymbolInfo>> RefreshSymbolListAsync(CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Force refreshing symbol list from Polygon API");
        return await FetchFullSymbolListAsync(cancellationToken);
    }

    public async Task<bool> IsCacheValidAsync(CancellationToken cancellationToken = default)
    {
        if (_redis == null)
        {
            return false;
        }

        try
        {
            var cacheDetails = await GetCacheDetailsAsync(cancellationToken);
            if (cacheDetails == null)
            {
                return false;
            }

            var cacheAge = DateTime.UtcNow - cacheDetails.FetchedAt;
            var isValid = cacheAge < TimeSpan.FromHours(_config.CacheTtlHours);

            _logger.LogDebug("Cache age: {CacheAge}, TTL: {TTL}h, Valid: {IsValid}",
                cacheAge, _config.CacheTtlHours, isValid);

            return isValid;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking cache validity");
            return false;
        }
    }

    public async Task<RedisPolygonSymbolList?> GetCacheDetailsAsync(CancellationToken cancellationToken = default)
    {
        if (_redis == null)
        {
            return null;
        }

        try
        {
            cancellationToken.ThrowIfCancellationRequested();
            var cacheJson = await _redis.StringGetAsync(RedisPolygonSymbolList.GetRedisKey());
            if (!cacheJson.HasValue)
            {
                return null;
            }

            return RedisPolygonSymbolList.FromJson(cacheJson!);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving cache details");
            return null;
        }
    }

    private async Task<IEnumerable<PolygonSymbolInfo>?> GetCachedSymbolListAsync(CancellationToken cancellationToken)
    {
        if (_redis == null)
        {
            return null;
        }

        try
        {
            cancellationToken.ThrowIfCancellationRequested();
            var cacheJson = await _redis.StringGetAsync(RedisPolygonSymbolList.GetRedisKey());
            if (!cacheJson.HasValue)
            {
                _logger.LogDebug("No cached symbol list found");
                return null;
            }

            var cachedList = RedisPolygonSymbolList.FromJson(cacheJson!);
            if (cachedList == null)
            {
                _logger.LogWarning("Failed to deserialize cached symbol list");
                return null;
            }

            // Check if cache is still valid
            var cacheAge = DateTime.UtcNow - cachedList.FetchedAt;
            if (cacheAge >= TimeSpan.FromHours(_config.CacheTtlHours))
            {
                _logger.LogInformation("Cached symbol list is stale (age: {CacheAge}), will fetch fresh data", cacheAge);
                return null;
            }

            _logger.LogDebug("Retrieved cached symbol list: {SymbolCount} symbols (fetched at {FetchedAt})",
                cachedList.Symbols.Count, cachedList.FetchedAt);

            return cachedList.Symbols;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving cached symbol list");
            return null;
        }
    }

    private string BuildInitialUrl()
    {
        var url = $"v3/reference/tickers?limit={_config.PageSize}";

        if (_config.IncludedMarkets.Any())
        {
            url += $"&market={string.Join(",", _config.IncludedMarkets)}";
        }

        if (_config.ActiveOnly)
        {
            url += "&active=true";
        }

        if (_config.IncludedTypes.Any())
        {
            url += $"&type={string.Join(",", _config.IncludedTypes)}";
        }

        return url;
    }

    private bool ShouldIncludeSymbol(PolygonTickerResult ticker)
    {
        // Basic validation
        if (string.IsNullOrEmpty(ticker.Ticker) || !ticker.Active)
        {
            return false;
        }

        // Market filter
        if (_config.IncludedMarkets.Any() && !_config.IncludedMarkets.Contains(ticker.Market))
        {
            return false;
        }

        // Type filter
        if (_config.IncludedTypes.Any() && !_config.IncludedTypes.Contains(ticker.Type))
        {
            return false;
        }

        // Additional filters can be added here
        return true;
    }

    private static PolygonSymbolInfo ConvertToSymbolInfo(PolygonTickerResult ticker)
    {
        return new PolygonSymbolInfo
        {
            Ticker = ticker.Ticker,
            Name = ticker.Name,
            Market = ticker.Market,
            Locale = ticker.Locale,
            PrimaryExchange = ticker.PrimaryExchange,
            Type = ticker.Type,
            Active = ticker.Active,
            CurrencyName = ticker.CurrencyName,
            Cik = ticker.Cik,
            CompositeFigi = ticker.CompositeFigi,
            ShareClassFigi = ticker.ShareClassFigi,
            LastUpdatedUtc = ticker.LastUpdatedUtc
        };
    }

    private async Task CacheSymbolListAsync(List<PolygonSymbolInfo> symbols, int apiCallCount, TimeSpan fetchDuration)
    {
        if (_redis == null)
        {
            return;
        }

        try
        {
            var cacheData = new RedisPolygonSymbolList
            {
                Symbols = symbols,
                FetchedAt = DateTime.UtcNow,
                TotalCount = symbols.Count,
                ApiCallCount = apiCallCount,
                FetchDuration = fetchDuration,
                Metadata = $"Fetched {symbols.Count} symbols in {fetchDuration.TotalSeconds:F1}s with {apiCallCount} API calls"
            };

            var ttl = TimeSpan.FromHours(_config.CacheTtlHours);
            await _redis.StringSetAsync(RedisPolygonSymbolList.GetRedisKey(), cacheData.ToJson(), ttl);

            _logger.LogDebug("Cached {SymbolCount} symbols with TTL {TTL}h", symbols.Count, _config.CacheTtlHours);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to cache symbol list in Redis");
        }
    }

    public void Dispose()
    {
        _connectionMultiplexer?.Dispose();
    }
}

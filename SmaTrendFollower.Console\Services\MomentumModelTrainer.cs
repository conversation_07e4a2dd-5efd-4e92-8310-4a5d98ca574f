using Microsoft.Extensions.Logging;
using SmaTrendFollower.Models;
using SmaTrendFollower.Services;
using SmaTrendFollower.Console.Extensions;
using StackExchange.Redis;
using System.Threading;

namespace SmaTrendFollower.Services;

/// <summary>
/// Thread-safe storage for momentum model thresholds
/// </summary>
internal static class MomentumThresholds
{
    private static double[] _current = new double[] { 0, 0 };

    public static double[] Current
        => Volatile.Read(ref _current);

    public static void Update(double[] w)
        => Interlocked.Exchange(ref _current, w);
}

/// <summary>
/// Interface for momentum model training with sentiment integration
/// </summary>
public interface IMomentumModelTrainer
{
    /// <summary>
    /// Creates feature vector for ML model including sentiment data
    /// </summary>
    Task<float[]> CreateFeatureVectorAsync(string symbol, DateTime date, CancellationToken cancellationToken = default);

    /// <summary>
    /// Trains momentum model with sentiment features
    /// </summary>
    Task<bool> TrainModelAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets sentiment score for a symbol on a specific date
    /// </summary>
    Task<double> GetSentimentScoreAsync(string symbol, DateTime date, CancellationToken cancellationToken = default);
}

/// <summary>
/// Momentum model trainer that incorporates news sentiment analysis into ML features
/// </summary>
public sealed class MomentumModelTrainer : IMomentumModelTrainer
{
    private readonly IDatabase _redisDatabase;
    private readonly ILogger<MomentumModelTrainer> _logger;
    private readonly IMomentumCache _momentumCache;
    private readonly IMarketDataService _marketDataService;

    public MomentumModelTrainer(
        IOptimizedRedisConnectionService redisConnectionService,
        ILogger<MomentumModelTrainer> logger,
        IMomentumCache momentumCache,
        IMarketDataService marketDataService)
    {
        _redisDatabase = redisConnectionService.GetDatabaseAsync().GetAwaiter().GetResult();
        _logger = logger;
        _momentumCache = momentumCache;
        _marketDataService = marketDataService;
    }

    /// <summary>
    /// Creates feature vector for ML model including sentiment data
    /// </summary>
    public async Task<float[]> CreateFeatureVectorAsync(string symbol, DateTime date, CancellationToken cancellationToken = default)
    {
        try
        {
            // Get basic momentum features
            var endDate = date.Date;
            var startDate = endDate.AddDays(-200); // Get 200 days of data
            var barsPage = await _marketDataService.GetStockBarsAsync(symbol, startDate, endDate);
            var bars = barsPage?.Items?.ToList();
            if (bars == null || bars.Count < 50)
            {
                _logger.LogWarning("Insufficient bar data for {Symbol}", symbol);
                return new float[] { 0, 0, 0 }; // Default feature vector
            }

            // Calculate RSI
            var rsi = bars.GetRsi14();

            // Calculate MACD
            var macd = bars.GetMacd();
            var macdHistogram = macd.Histogram;

            // Get sentiment score
            var sentiment = await GetSentimentScoreAsync(symbol, date, cancellationToken);

            // Create feature vector: [RSI, MACD Histogram, Sentiment]
            var features = new float[]
            {
                (float)rsi,
                (float)macdHistogram,
                (float)sentiment
            };

            _logger.LogDebug("Created feature vector for {Symbol}: RSI={RSI:F2}, MACD={MACD:F4}, Sentiment={Sentiment:F3}",
                symbol, rsi, macdHistogram, sentiment);

            return features;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating feature vector for {Symbol}", symbol);
            return new float[] { 0, 0, 0 }; // Default feature vector on error
        }
    }

    /// <summary>
    /// Gets sentiment score for a symbol on a specific date
    /// </summary>
    public async Task<double> GetSentimentScoreAsync(string symbol, DateTime date, CancellationToken cancellationToken = default)
    {
        try
        {
            var dateKey = date.ToString("yyyyMMdd");
            var sentimentKey = $"Sentiment:{symbol}:{dateKey}";

            // Try to get the latest sentiment score for the symbol on the given date
            var latestSentiment = await _redisDatabase.HashGetAsync(sentimentKey, "latest");
            
            if (latestSentiment.HasValue && double.TryParse(latestSentiment, out var sentiment))
            {
                _logger.LogDebug("Retrieved sentiment for {Symbol} on {Date}: {Sentiment:F3}", 
                    symbol, dateKey, sentiment);
                return sentiment;
            }

            // If no latest sentiment, try to get any sentiment from that day
            var allSentiments = await _redisDatabase.HashGetAllAsync(sentimentKey);
            if (allSentiments.Any())
            {
                var sentimentValues = allSentiments
                    .Where(kv => kv.Name != "latest" && !string.IsNullOrEmpty(kv.Value) && double.TryParse(kv.Value, out _))
                    .Select(kv => double.Parse(kv.Value!))
                    .ToList();

                if (sentimentValues.Any())
                {
                    // Return average sentiment for the day
                    var avgSentiment = sentimentValues.Average();
                    _logger.LogDebug("Retrieved average sentiment for {Symbol} on {Date}: {Sentiment:F3} (from {Count} news items)",
                        symbol, dateKey, avgSentiment, sentimentValues.Count);
                    return avgSentiment;
                }
            }

            // No sentiment data found, return neutral
            _logger.LogDebug("No sentiment data found for {Symbol} on {Date}, returning neutral (0.0)", symbol, dateKey);
            return 0.0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving sentiment for {Symbol} on {Date}", symbol, date);
            return 0.0; // Return neutral sentiment on error
        }
    }

    /// <summary>
    /// Trains momentum model with sentiment features
    /// </summary>
    public async Task<bool> TrainModelAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Starting momentum model training with sentiment features...");

            // Collect training data
            var trainingData = await CollectTrainingDataAsync(cancellationToken);
            if (trainingData.Count < 100)
            {
                _logger.LogWarning("Insufficient training data: {Count} samples", trainingData.Count);
                return false;
            }

            // Prepare feature matrix and target vector
            var features = trainingData.Select(d => d.Features).ToArray();
            var targets = trainingData.Select(d => d.Target).ToArray();

            // Train OLS model
            var ols = new SimpleOLSRegression();
            ols.Fit(features, targets);

            // Update thresholds with thread-safe operation
            MomentumThresholds.Update(ols.Weights);

            _logger.LogInformation("Momentum model training completed successfully. Weights: [{Weights}]",
                string.Join(", ", ols.Weights.Select(w => w.ToString("F4"))));

            return true;
        }
        catch (OperationCanceledException)
        {
            _logger.LogInformation("Momentum model training was cancelled");
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during momentum model training");
            return false;
        }
    }

    /// <summary>
    /// Collects training data for momentum model
    /// </summary>
    private async Task<List<TrainingDataPoint>> CollectTrainingDataAsync(CancellationToken cancellationToken)
    {
        var trainingData = new List<TrainingDataPoint>();
        var endDate = DateTime.UtcNow.Date;
        var startDate = endDate.AddDays(-365); // One year of training data

        try
        {
            // Get universe of symbols (simplified - would use actual universe provider)
            var symbols = new[] { "SPY", "QQQ", "IWM", "AAPL", "MSFT", "GOOGL", "AMZN", "TSLA", "NVDA", "META" };

            foreach (var symbol in symbols)
            {
                cancellationToken.ThrowIfCancellationRequested();

                var currentDate = startDate;
                while (currentDate <= endDate)
                {
                    try
                    {
                        var features = await CreateFeatureVectorAsync(symbol, currentDate, cancellationToken);

                        // Calculate target (next day return)
                        var nextDate = currentDate.AddDays(1);
                        var target = await CalculateNextDayReturnAsync(symbol, currentDate, nextDate, cancellationToken);

                        if (target.HasValue && features.Length == 3)
                        {
                            trainingData.Add(new TrainingDataPoint
                            {
                                Features = features,
                                Target = (double)target.Value
                            });
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogDebug(ex, "Error collecting training data for {Symbol} on {Date}", symbol, currentDate);
                    }

                    currentDate = currentDate.AddDays(1);
                }
            }

            _logger.LogInformation("Collected {Count} training data points", trainingData.Count);
            return trainingData;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error collecting training data");
            return trainingData;
        }
    }

    /// <summary>
    /// Calculates next day return for target variable
    /// </summary>
    private async Task<decimal?> CalculateNextDayReturnAsync(string symbol, DateTime currentDate, DateTime nextDate, CancellationToken cancellationToken)
    {
        try
        {
            var currentBars = await _marketDataService.GetStockBarsAsync(symbol, currentDate, currentDate.AddDays(1));
            var nextBars = await _marketDataService.GetStockBarsAsync(symbol, nextDate, nextDate.AddDays(1));

            var currentBar = currentBars?.Items?.FirstOrDefault();
            var nextBar = nextBars?.Items?.FirstOrDefault();

            if (currentBar != null && nextBar != null && currentBar.Close > 0)
            {
                return (nextBar.Close - currentBar.Close) / currentBar.Close;
            }

            return null;
        }
        catch (Exception ex)
        {
            _logger.LogDebug(ex, "Error calculating return for {Symbol}", symbol);
            return null;
        }
    }
}

/// <summary>
/// Training data point for momentum model
/// </summary>
internal class TrainingDataPoint
{
    public float[] Features { get; set; } = Array.Empty<float>();
    public double Target { get; set; }
}

/// <summary>
/// Simple OLS regression implementation
/// </summary>
internal class SimpleOLSRegression
{
    public double[] Weights { get; private set; } = Array.Empty<double>();

    public void Fit(float[][] features, double[] targets)
    {
        if (features.Length != targets.Length)
            throw new ArgumentException("Features and targets must have same length");

        var n = features.Length;
        var p = features[0].Length + 1; // +1 for intercept

        // Create design matrix with intercept
        var X = new double[n, p];
        for (int i = 0; i < n; i++)
        {
            X[i, 0] = 1.0; // Intercept
            for (int j = 0; j < features[i].Length; j++)
            {
                X[i, j + 1] = features[i][j];
            }
        }

        // Calculate weights using normal equation: w = (X'X)^-1 X'y
        var XtX = MatrixMultiplyTranspose(X);
        var XtXInv = MatrixInverse(XtX);
        var Xty = MatrixVectorMultiplyTranspose(X, targets);

        Weights = MatrixVectorMultiply(XtXInv, Xty);
    }

    private double[,] MatrixMultiplyTranspose(double[,] matrix)
    {
        var rows = matrix.GetLength(0);
        var cols = matrix.GetLength(1);
        var result = new double[cols, cols];

        for (int i = 0; i < cols; i++)
        {
            for (int j = 0; j < cols; j++)
            {
                double sum = 0;
                for (int k = 0; k < rows; k++)
                {
                    sum += matrix[k, i] * matrix[k, j];
                }
                result[i, j] = sum;
            }
        }
        return result;
    }

    private double[] MatrixVectorMultiplyTranspose(double[,] matrix, double[] vector)
    {
        var rows = matrix.GetLength(0);
        var cols = matrix.GetLength(1);
        var result = new double[cols];

        for (int i = 0; i < cols; i++)
        {
            double sum = 0;
            for (int j = 0; j < rows; j++)
            {
                sum += matrix[j, i] * vector[j];
            }
            result[i] = sum;
        }
        return result;
    }

    private double[,] MatrixInverse(double[,] matrix)
    {
        var n = matrix.GetLength(0);
        var result = new double[n, n];
        var identity = new double[n, n];

        // Create identity matrix
        for (int i = 0; i < n; i++)
            identity[i, i] = 1.0;

        // Copy input matrix
        for (int i = 0; i < n; i++)
            for (int j = 0; j < n; j++)
                result[i, j] = matrix[i, j];

        // Gaussian elimination with partial pivoting
        for (int i = 0; i < n; i++)
        {
            // Find pivot
            var maxRow = i;
            for (int k = i + 1; k < n; k++)
            {
                if (Math.Abs(result[k, i]) > Math.Abs(result[maxRow, i]))
                    maxRow = k;
            }

            // Swap rows
            for (int k = 0; k < n; k++)
            {
                (result[i, k], result[maxRow, k]) = (result[maxRow, k], result[i, k]);
                (identity[i, k], identity[maxRow, k]) = (identity[maxRow, k], identity[i, k]);
            }

            // Make diagonal element 1
            var pivot = result[i, i];
            if (Math.Abs(pivot) < 1e-10)
                throw new InvalidOperationException("Matrix is singular");

            for (int k = 0; k < n; k++)
            {
                result[i, k] /= pivot;
                identity[i, k] /= pivot;
            }

            // Eliminate column
            for (int k = 0; k < n; k++)
            {
                if (k != i)
                {
                    var factor = result[k, i];
                    for (int j = 0; j < n; j++)
                    {
                        result[k, j] -= factor * result[i, j];
                        identity[k, j] -= factor * identity[i, j];
                    }
                }
            }
        }

        return identity;
    }

    private double[] MatrixVectorMultiply(double[,] matrix, double[] vector)
    {
        var rows = matrix.GetLength(0);
        var cols = matrix.GetLength(1);
        var result = new double[rows];

        for (int i = 0; i < rows; i++)
        {
            double sum = 0;
            for (int j = 0; j < cols; j++)
            {
                sum += matrix[i, j] * vector[j];
            }
            result[i] = sum;
        }
        return result;
    }
}

using SmaTrendFollower.Console.Extensions;
using SmaTrendFollower.Console.Configuration;
using Alpaca.Markets;
using SmaTrendFollower.Models;
using SmaTrendFollower.MachineLearning.Prediction;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.DependencyInjection;
using System.Collections.Concurrent;
using System.Diagnostics;
using SmaTrendFollower.Monitoring;

namespace SmaTrendFollower.Services;

/// <summary>
/// ML-enhanced signal generator that combines traditional technical analysis with machine learning.
/// Uses trained ML models to score and filter trading signals for improved performance.
/// </summary>
public sealed class MLEnhancedSignalGenerator : ISignalGenerator, IDisposable
{
    private readonly IMarketDataService _marketDataService;
    private readonly IUniverseProvider _universeProvider;
    private readonly ILiveStateStore _liveStateStore;
    private readonly IMomentumFilter _momentumFilter;
    private readonly IVolatilityFilter _volatilityFilter;
    private readonly IPositionSizer _positionSizer;
    private readonly ISignalRanker _signalRanker;
    private readonly ILogger<MLEnhancedSignalGenerator> _logger;
    private readonly IServiceProvider _serviceProvider;
    private readonly ParallelOptions _parallelOptions;
    private readonly SemaphoreSlim _rateGate = new(20); // 20 concurrent calls ≈ 80-90 req/s
    private readonly TimeoutConfiguration _timeouts;
    private readonly float _mlProbabilityThreshold;

    public MLEnhancedSignalGenerator(
        IMarketDataService marketDataService,
        IUniverseProvider universeProvider,
        ILiveStateStore liveStateStore,
        IMomentumFilter momentumFilter,
        IVolatilityFilter volatilityFilter,
        IPositionSizer positionSizer,
        ISignalRanker signalRanker,
        ILogger<MLEnhancedSignalGenerator> logger,
        IServiceProvider serviceProvider,
        TimeoutConfiguration? timeouts = null,
        float mlProbabilityThreshold = 0.65f)
    {
        _marketDataService = marketDataService;
        _universeProvider = universeProvider;
        _liveStateStore = liveStateStore;
        _momentumFilter = momentumFilter;
        _volatilityFilter = volatilityFilter;
        _positionSizer = positionSizer;
        _signalRanker = signalRanker;
        _logger = logger;
        _serviceProvider = serviceProvider;
        _timeouts = timeouts ?? new TimeoutConfiguration();
        _mlProbabilityThreshold = mlProbabilityThreshold;

        _parallelOptions = new ParallelOptions
        {
            MaxDegreeOfParallelism = Environment.ProcessorCount,
            CancellationToken = CancellationToken.None
        };
    }

    public async Task<IEnumerable<TradingSignal>> RunAsync(int topN = 10, CancellationToken cancellationToken = default)
    {
        var totalStopwatch = Stopwatch.StartNew();

        try
        {
            _logger.LogInformation("Starting ML-enhanced signal generation (ML threshold: {Threshold:P1})", 
                _mlProbabilityThreshold);

            // Check if ML model is available
            if (!_signalRanker.IsModelLoaded)
            {
                _logger.LogWarning("ML model not loaded, falling back to traditional signal generation");
            }

            // Step 1: Check market conditions first
            var marketVolatility = await _volatilityFilter.GetMarketVolatilityAsync();
            if (!marketVolatility.IsEligible)
            {
                _logger.LogWarning("Market conditions unfavorable for trading: {Reason}", marketVolatility.Reason);
                return Enumerable.Empty<TradingSignal>();
            }

            _logger.LogInformation("Market conditions favorable: {Regime} volatility, VIX {VIX:F1}",
                marketVolatility.Regime, marketVolatility.VixLevel);

            // Step 2: Get universe and fetch data
            var symbols = await _universeProvider.GetSymbolsAsync();
            var symbolList = symbols.Distinct().ToList();

            _logger.LogInformation("Screening {Count} symbols with ML-enhanced filtering", symbolList.Count);

            var symbolDataMap = await FetchDataInParallelAsync(symbolList);
            _logger.LogInformation("Fetched data for {Count}/{Total} symbols", symbolDataMap.Count, symbolList.Count);

            // Step 3: Apply traditional filters and generate initial signals
            var traditionalSignals = ApplyTraditionalFilters(symbolDataMap, marketVolatility);
            _logger.LogInformation("Generated {Count} traditional signals", traditionalSignals.Count);

            if (!traditionalSignals.Any())
            {
                _logger.LogInformation("No traditional signals generated");
                return Enumerable.Empty<TradingSignal>();
            }

            // Step 4: Apply ML filtering and ranking
            var mlEnhancedSignals = ApplyMLFiltering(traditionalSignals, symbolDataMap, marketVolatility);
            _logger.LogInformation("ML filtering: {MLCount} signals passed threshold", mlEnhancedSignals.Count);

            // Step 5: Final ranking and selection
            var finalSignals = mlEnhancedSignals
                .Take(topN)
                .ToList();

            totalStopwatch.Stop();

            _logger.LogInformation("ML-enhanced signal generation completed: {Count} signals from {Total} symbols in {ElapsedMs:F0}ms",
                finalSignals.Count, symbolList.Count, totalStopwatch.Elapsed.TotalMilliseconds);

            // Log ML model info if available
            var modelInfo = _signalRanker.GetModelInfo();
            if (modelInfo != null)
            {
                _logger.LogInformation("ML model: {Version} (Accuracy: {Accuracy:P2}, Trained: {TrainedAt:yyyy-MM-dd})",
                    modelInfo.ModelVersion, modelInfo.Accuracy, modelInfo.TrainedAt);
            }

            return finalSignals;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in ML-enhanced signal generation");
            return Enumerable.Empty<TradingSignal>();
        }
    }

    /// <summary>
    /// Fetches market data for all symbols using parallel async I/O with rate limiting
    /// </summary>
    private async Task<ConcurrentDictionary<string, List<IBar>>> FetchDataInParallelAsync(List<string> symbols)
    {
        var dataStopwatch = Stopwatch.StartNew();
        var symbolDataMap = new ConcurrentDictionary<string, List<IBar>>();

        var fetchTasks = symbols.Select(async symbol =>
        {
            var sw = Stopwatch.StartNew();
            await _rateGate.WaitAsync();
            try
            {
                var startDate = DateTime.UtcNow.AddDays(-300);
                var endDate = DateTime.UtcNow;

                var response = await _marketDataService.GetStockBarsAsync(symbol, startDate, endDate);
                var bars = response.Items.ToList();

                if (bars.Count >= 200)
                {
                    symbolDataMap[symbol] = bars;
                }
                else
                {
                    _logger.LogDebug("Insufficient bars for {Symbol}: {Count} (need 200+)", symbol, bars.Count);
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to fetch data for {Symbol}", symbol);
            }
            finally
            {
                _rateGate.Release();
                MetricsRegistry.SignalLatencyMs.Observe(sw.Elapsed.TotalMilliseconds);
            }
        });

        using var timeoutCts = new CancellationTokenSource(_timeouts.MarketData.ParallelFetch);
        try
        {
            await Task.WhenAll(fetchTasks).WaitAsync(timeoutCts.Token);
        }
        catch (OperationCanceledException) when (timeoutCts.Token.IsCancellationRequested)
        {
            _logger.LogWarning("Parallel data fetch timed out after {Timeout}ms", _timeouts.MarketData.ParallelFetch.TotalMilliseconds);
        }

        dataStopwatch.Stop();
        _logger.LogInformation("Parallel data fetch completed in {ElapsedMs:F0}ms", dataStopwatch.Elapsed.TotalMilliseconds);

        return symbolDataMap;
    }

    /// <summary>
    /// Applies traditional technical analysis filters
    /// </summary>
    private List<TradingSignal> ApplyTraditionalFilters(
        ConcurrentDictionary<string, List<IBar>> symbolDataMap,
        MarketVolatilityAnalysis marketVolatility)
    {
        var signals = new ConcurrentBag<TradingSignal>();

        Parallel.ForEach(symbolDataMap, kvp =>
        {
            var symbol = kvp.Key;
            var bars = kvp.Value;

            try
            {
                // Apply momentum filter
                var momentumEligible = _momentumFilter.IsEligible(symbol, bars);
                if (!momentumEligible)
                {
                    return;
                }

                // Apply volatility filter
                var volatilityAnalysis = _volatilityFilter.AnalyzeSymbolVolatility(symbol, bars);
                if (!volatilityAnalysis.IsEligible)
                {
                    return;
                }

                // Calculate technical indicators
                var currentPrice = bars.Last().Close;
                var sma50 = (decimal)bars.GetSma50();
                var sma200 = (decimal)bars.GetSma200();
                var atr14 = (decimal)bars.GetAtr14();
                var sixMonthReturn = (decimal)bars.GetTotalReturn(126);

                // Basic trend filter: close > sma50 && close > sma200
                if (currentPrice > sma50 && currentPrice > sma200 && atr14 > 0)
                {
                    var signal = new TradingSignal(symbol, currentPrice, atr14, sixMonthReturn);
                    signals.Add(signal);
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error processing traditional filters for {Symbol}", symbol);
            }
        });

        return signals.ToList();
    }

    /// <summary>
    /// Applies ML filtering and ranking to traditional signals
    /// </summary>
    private List<TradingSignal> ApplyMLFiltering(
        List<TradingSignal> traditionalSignals,
        ConcurrentDictionary<string, List<IBar>> symbolDataMap,
        MarketVolatilityAnalysis marketVolatility)
    {
        if (!_signalRanker.IsModelLoaded)
        {
            _logger.LogWarning("ML model not loaded, returning traditional signals");
            return traditionalSignals.OrderByDescending(s => s.SixMonthReturn).ToList();
        }

        try
        {
            // Extract features for ML scoring
            var mlScoredSignals = _signalRanker.ScoreAndRank(traditionalSignals, signal =>
            {
                var bars = symbolDataMap[signal.Symbol];
                return ExtractSignalFeatures(signal, bars, marketVolatility);
            });

            // Filter by ML probability threshold and return ranked results
            var filteredSignals = mlScoredSignals
                .Where(x => x.Probability >= _mlProbabilityThreshold)
                .OrderByDescending(x => x.Score)
                .Select(x => x.Signal)
                .ToList();

            _logger.LogInformation("ML ranking: {FilteredCount}/{TotalCount} signals passed probability threshold {Threshold:P1}",
                filteredSignals.Count, traditionalSignals.Count, _mlProbabilityThreshold);

            return filteredSignals;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in ML filtering, falling back to traditional ranking");
            return traditionalSignals.OrderByDescending(s => s.SixMonthReturn).ToList();
        }
    }

    /// <summary>
    /// Extracts features for ML model from signal and market data
    /// </summary>
    private SignalFeatures ExtractSignalFeatures(
        TradingSignal signal,
        List<IBar> bars,
        MarketVolatilityAnalysis marketVolatility)
    {
        var currentPrice = bars.Last().Close;
        var sma50 = bars.GetSma50();
        var rsi14 = bars.GetRsi14();
        var volume20Avg = bars.TakeLast(20).Average(b => (double)b.Volume);
        var currentVolume = (double)bars.Last().Volume;

        // Get sentiment score for today (synchronous call for now)
        var sentiment = 0.0f;
        try
        {
            var momentumTrainer = _serviceProvider.GetService<IMomentumModelTrainer>();
            if (momentumTrainer != null)
            {
                sentiment = (float)momentumTrainer.GetSentimentScoreAsync(signal.Symbol, DateTime.UtcNow).GetAwaiter().GetResult();
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to get sentiment for {Symbol}, using neutral", signal.Symbol);
        }

        return new SignalFeatures(
            SmaGap: (float)(currentPrice / (decimal)sma50),
            Volatility: (float)(signal.Atr / signal.Price),
            Rsi: (float)rsi14,
            BreadthScore: 0.7f, // TODO: Implement breadth calculation
            VixLevel: (float)marketVolatility.VixLevel,
            SixMonthReturn: (float)signal.SixMonthReturn,
            RelativeVolume: (float)(currentVolume / volume20Avg),
            MarketRegime: marketVolatility.Regime switch
            {
                MarketVolatilityRegime.Low => 1.0f,
                MarketVolatilityRegime.Normal => 0.6f,
                MarketVolatilityRegime.Elevated => 0.4f,
                MarketVolatilityRegime.High => 0.3f,
                MarketVolatilityRegime.Crisis => 0.1f,
                _ => 0.5f
            },
            Sentiment: sentiment
        );
    }

    public void Dispose()
    {
        _rateGate?.Dispose();
        GC.SuppressFinalize(this);
    }
}
